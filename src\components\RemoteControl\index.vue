<template>
  <div class="remote-control-container">
    <!-- 远程控制按钮 -->
    <div class="remote-control-buttons">
      <div
        class="control-button assist-button"
        :class="{ active: isAssistedMode }"
        @click="startAssistMode"
        title="协助按钮（被控制端使用）"
      >
        <i class="icon-assist"></i>
        <span>协助</span>
      </div>

      <div
        class="control-button control-button-main"
        :class="{ active: isControllerMode }"
        @click="startControlMode"
        title="控制按钮（控制端使用）"
      >
        <i class="icon-control"></i>
        <span>控制</span>
      </div>
    </div>

    <!-- 控制码显示对话框 -->
    <el-dialog
      title="远程协助"
      :visible.sync="showControlCodeDialog"
      width="400px"
      :close-on-click-modal="false"
      custom-class="remote-control-dialog cadcs-dialog"
      :z-index="3000"
      :modal-append-to-body="true"
      append-to-body
    >
      <div class="cadcs-dialog__content">
        <div class="control-code-content">
          <div class="code-display">
            <div class="code-label">控制码</div>
            <div class="code-value">{{ controlCode }}</div>
            <div
              class="cadcs-dialog__footer-submit copy-button"
              @click="copyControlCode"
            >
              复制
            </div>
          </div>
          <div class="code-tips">
            <p>请将此控制码告知控制端，控制码有效期10分钟</p>
            <p>等待控制端连接...</p>
          </div>
        </div>
      </div>
      <div class="cadcs-dialog__footer">
        <div class="cadcs-dialog__footer-reset" @click="stopAssistMode">取消</div>
      </div>
    </el-dialog>

    <!-- 输入控制码对话框 -->
    <el-dialog
      title="远程控制"
      :visible.sync="showInputCodeDialog"
      width="400px"
      :close-on-click-modal="false"
      custom-class="remote-control-dialog cadcs-dialog is-form"
      :z-index="3000"
      :modal-append-to-body="true"
      append-to-body
    >
      <div class="cadcs-dialog__content">
        <div class="input-code-content">
          <el-form :model="inputForm" :rules="inputRules" ref="inputForm" class="my-form">
            <el-form-item label="控制码" prop="controlCode">
              <el-input
                v-model="inputForm.controlCode"
                placeholder="请输入6位数字控制码"
                maxlength="6"
                @keyup.enter.native="connectToSession"
                class="filter-item"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="cadcs-dialog__footer">
        <div class="cadcs-dialog__footer-reset" @click="stopControlMode">取消</div>
        <div
          class="cadcs-dialog__footer-submit"
          @click="connectToSession"
          :class="{ 'loading': connecting }"
        >
          {{ connecting ? '连接中...' : '连接' }}
        </div>
      </div>
    </el-dialog>

    <!-- 连接状态显示 -->
    <el-dialog
      title="远程控制状态"
      :visible.sync="showStatusDialog"
      width="450px"
      :close-on-click-modal="false"
      custom-class="remote-control-dialog cadcs-dialog"
      :z-index="3000"
      :modal-append-to-body="true"
      append-to-body
    >
      <div class="cadcs-dialog__content">
        <div class="status-content">
          <div class="status-info">
            <div class="status-item">
              <span class="label">连接状态：</span>
              <span class="value" :class="connectionStatusClass">{{ connectionStatusText }}</span>
            </div>
            <div class="status-item">
              <span class="label">角色：</span>
              <span class="value">{{ roleText }}</span>
            </div>
            <div v-if="controlCode" class="status-item">
              <span class="label">控制码：</span>
              <span class="value">{{ controlCode }}</span>
            </div>
          </div>

          <!-- 控制端操作区域 -->
          <div v-if="isControllerMode && isConnected" class="control-actions">
            <div class="action-title">控制操作</div>
            <div class="action-buttons">
              <div
                class="cadcs-dialog__footer-submit action-button"
                @click="sendZoomInCommand"
              >
                地图放大
              </div>
              <div
                class="cadcs-dialog__footer-submit action-button"
                @click="sendZoomOutCommand"
              >
                地图缩小
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="cadcs-dialog__footer">
        <div class="cadcs-dialog__footer-reset danger" @click="stopRemoteControl">停止控制</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RemoteControlWebSocketClient from './RemoteControlWebSocketClient'

export default {
  name: 'RemoteControl',
  data() {
    return {
      // WebSocket客户端
      wsClient: null,

      // 状态控制
      isAssistedMode: false,
      isControllerMode: false,
      isConnected: false,
      connecting: false,

      // 对话框显示控制
      showControlCodeDialog: false,
      showInputCodeDialog: false,
      showStatusDialog: false,

      // 控制码
      controlCode: '',

      // 输入表单
      inputForm: {
        controlCode: ''
      },
      inputRules: {
        controlCode: [
          { required: true, message: '请输入控制码', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '控制码必须是6位数字', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    connectionStatusText() {
      if (this.connecting) return '连接中...'
      if (this.isConnected) return '已连接'
      return '未连接'
    },
    connectionStatusClass() {
      if (this.connecting) return 'connecting'
      if (this.isConnected) return 'connected'
      return 'disconnected'
    },
    roleText() {
      if (this.isAssistedMode) return '被控制端'
      if (this.isControllerMode) return '控制端'
      return '未设置'
    }
  },
  mounted() {
    console.log('RemoteControl组件mounted')
    console.log('window.REMOTE_CONTROL_SERVICES:', window.REMOTE_CONTROL_SERVICES)
    this.initWebSocketClient()
  },
  beforeDestroy() {
    if (this.wsClient) {
      this.wsClient.disconnect()
    }
  },
  methods: {
    /**
     * 初始化WebSocket客户端
     */
    initWebSocketClient() {
      this.wsClient = new RemoteControlWebSocketClient()

      // 监听连接事件
      this.wsClient.on('connected', () => {
        console.log('远程控制WebSocket已连接')
      })

      // 监听断开连接事件
      this.wsClient.on('disconnected', () => {
        console.log('远程控制WebSocket已断开')
        this.isConnected = false
        this.connecting = false

        // 断开连接时关闭状态dialog
        if (this.showStatusDialog) {
          this.showStatusDialog = false
          this.$message.warning('远程控制连接已断开')
        }
      })

      // 监听控制码生成事件
      this.wsClient.on('controlCodeGenerated', (message) => {
        this.controlCode = message.controlCode
        this.$message.success('控制码生成成功')
      })

      // 监听会话状态事件
      this.wsClient.on('sessionStatus', (message) => {
        this.$message.info(message.message)
        if (message.message.includes('连接建立成功')) {
          this.isConnected = true
          this.connecting = false

          // 控制端：关闭输入对话框，显示状态对话框
          if (this.isControllerMode) {
            this.showInputCodeDialog = false
            this.showStatusDialog = true
          }

          // 被控制端：关闭控制码对话框，显示状态对话框
          if (this.isAssistedMode) {
            this.showControlCodeDialog = false
            this.showStatusDialog = true
          }
        }
      })

      // 监听控制命令事件
      this.wsClient.on('command', (message) => {
        this.handleRemoteCommand(message)
      })

      // 监听错误事件
      this.wsClient.on('error', (error) => {
        console.error('远程控制错误:', error)
        this.$message.error(error.message || '远程控制发生错误')
        this.connecting = false
      })
    },

    /**
     * 开始协助模式（被控制端）
     */
    startAssistMode() {
      if (this.isControllerMode) {
        this.$message.warning('请先停止控制模式')
        return
      }

      this.isAssistedMode = true
      this.wsClient.connect()

      // 连接成功后请求控制码
      const onConnected = () => {
        this.wsClient.requestControlCode()
        this.showControlCodeDialog = true
        // 移除事件监听器，避免重复绑定
        this.wsClient.off('connected', onConnected)
      }

      this.wsClient.on('connected', onConnected)
    },

    /**
     * 开始控制模式（控制端）
     */
    startControlMode() {
      console.log('点击控制按钮，开始控制模式')

      if (this.isAssistedMode) {
        this.$message.warning('请先停止协助模式')
        return
      }

      console.log('设置控制端模式，准备连接WebSocket')
      this.isControllerMode = true

      // 连接WebSocket
      this.wsClient.connect()

      // 连接成功后显示输入控制码对话框
      const onConnected = () => {
        console.log('WebSocket连接成功，显示输入控制码对话框')
        this.showInputCodeDialog = true
        // 移除事件监听器，避免重复绑定
        this.wsClient.off('connected', onConnected)
      }

      this.wsClient.on('connected', onConnected)
    },

    /**
     * 停止协助模式
     */
    stopAssistMode() {
      this.isAssistedMode = false
      this.showControlCodeDialog = false
      this.showStatusDialog = false
      this.controlCode = ''
      this.isConnected = false

      if (this.wsClient) {
        this.wsClient.disconnect()
      }
    },

    /**
     * 停止控制模式
     */
    stopControlMode() {
      this.isControllerMode = false
      this.showInputCodeDialog = false
      this.showStatusDialog = false
      this.connecting = false
      this.inputForm.controlCode = ''

      if (this.wsClient) {
        this.wsClient.disconnect()
      }
    },

    /**
     * 停止远程控制
     */
    stopRemoteControl() {
      this.showStatusDialog = false
      this.isConnected = false

      if (this.isAssistedMode) {
        this.stopAssistMode()
      } else if (this.isControllerMode) {
        this.stopControlMode()
      }
    },

    /**
     * 连接到会话
     */
    connectToSession() {
      this.$refs.inputForm.validate((valid) => {
        if (!valid) return

        this.connecting = true
        this.wsClient.joinSession(this.inputForm.controlCode)
      })
    },

    /**
     * 复制控制码
     */
    copyControlCode() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.controlCode).then(() => {
          this.$message.success('控制码已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyControlCode()
        })
      } else {
        this.fallbackCopyControlCode()
      }
    },

    /**
     * 备用复制方法
     */
    fallbackCopyControlCode() {
      const textArea = document.createElement('textarea')
      textArea.value = this.controlCode
      document.body.appendChild(textArea)
      textArea.select()

      try {
        document.execCommand('copy')
        this.$message.success('控制码已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }

      document.body.removeChild(textArea)
    },

    /**
     * 发送地图放大命令
     */
    sendZoomInCommand() {
      if (!this.isConnected) {
        this.$message.warning('未连接到远程会话')
        return
      }

      this.wsClient.sendCommand('MAP_ZOOM_IN', {
        factor: 1.5
      })
      this.$message.info('已发送地图放大命令')
    },

    /**
     * 发送地图缩小命令
     */
    sendZoomOutCommand() {
      if (!this.isConnected) {
        this.$message.warning('未连接到远程会话')
        return
      }

      this.wsClient.sendCommand('MAP_ZOOM_OUT', {
        factor: 0.75
      })
      this.$message.info('已发送地图缩小命令')
    },

    /**
     * 处理远程命令
     */
    handleRemoteCommand(message) {
      const { commandType, commandData } = message

      console.log('收到远程控制命令:', commandType, commandData)

      // 通过EventBus发送命令
      if (window.Vue && window.Vue.prototype && window.Vue.prototype.$bus) {
        window.Vue.prototype.$bus.$emit('remote-control-command', {
          type: commandType,
          data: commandData
        })
      } else {
        console.warn('EventBus未初始化，无法处理远程控制命令')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.remote-control-container {
  .remote-control-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .control-button {
      width: 60px;
      height: 60px;
      background: rgba(0, 123, 255, 0.8);
      border: 2px solid #007bff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: white;
      font-size: 12px;

      &:hover {
        background: rgba(0, 123, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
      }

      &.active {
        background: #28a745;
        border-color: #28a745;

        &:hover {
          background: #218838;
        }
      }

      .icon-assist::before {
        content: "🤝";
        font-size: 20px;
      }

      .icon-control::before {
        content: "🎮";
        font-size: 20px;
      }

      span {
        margin-top: 4px;
        font-weight: bold;
      }
    }
  }
}
</style>

<style lang="scss">
@import "@/assets/scss/variable.scss";

/* 确保遮罩层也有正确的z-index */
.v-modal {
  z-index: 2999 !important;
}

.remote-control-dialog {
  z-index: 3000 !important;

  .el-dialog__wrapper {
    z-index: 3000 !important;
  }

  .el-dialog {
    z-index: 3001 !important;
  }

  .control-code-content {
    text-align: center;
    padding: 20px 0;

    .code-display {
      margin-bottom: 30px;

      .code-label {
        font-size: 16px;
        color: rgb(107, 172, 222);
        margin-bottom: 15px;
        font-weight: 600;
      }

      .code-value {
        font-size: 36px;
        font-weight: bold;
        color: #5cdbd3;
        letter-spacing: 8px;
        margin-bottom: 20px;
        font-family: 'Courier New', monospace;
        text-shadow: 0 0 10px rgba(92, 219, 211, 0.5);
      }

      .copy-button {
        margin-top: 15px;
      }
    }

    .code-tips {
      color: rgb(107, 172, 222);
      font-size: 14px;
      line-height: 1.6;

      p {
        margin: 8px 0;
      }
    }
  }

  .input-code-content {
    padding: 20px 0;

    .el-input__inner {
      text-align: center;
      font-size: 20px;
      letter-spacing: 6px;
      font-family: 'Courier New', monospace;
      height: 45px;
      line-height: 45px;
    }
  }

  .status-content {
    padding: 20px 0;

    .status-info {
      margin-bottom: 25px;

      .status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding: 8px 0;
        border-bottom: 1px solid rgba(230, 255, 251, 0.2);

        .label {
          font-weight: 600;
          color: rgb(107, 172, 222);
        }

        .value {
          font-weight: 500;

          &.connected {
            color: #5cdbd3;
            text-shadow: 0 0 5px rgba(92, 219, 211, 0.5);
          }

          &.connecting {
            color: #ffc107;
            text-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
          }

          &.disconnected {
            color: #ff6b6b;
            text-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
          }
        }
      }
    }

    .control-actions {
      border-top: 1px solid rgba(230, 255, 251, 0.3);
      padding-top: 20px;

      .action-title {
        font-weight: 600;
        margin-bottom: 15px;
        color: rgb(107, 172, 222);
        text-align: center;
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
      }

      .action-button {
        margin: 5px;
        min-width: 100px;
        font-size: 14px;
      }
    }
  }

  // 危险按钮样式
  .cadcs-dialog__footer-reset.danger {
    background-image: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border-color: #ff6b6b;

    &:hover {
      background-image: linear-gradient(45deg, #ff5252, #d32f2f);
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    }
  }

  // 加载状态
  .loading {
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      background-image: url("/assets/img/head/btn-sel.png") !important;
    }
  }
}
</style>
